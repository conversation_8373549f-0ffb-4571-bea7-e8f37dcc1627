<!-- Compact Gallery Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Photo Gallery</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Share your best moments and showcase your personality</p>
</div>

<div class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <!-- Upload Area -->
        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50 hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 cursor-pointer" id="upload-area">
            <input type="file" id="gallery-upload" multiple accept="image/*" class="hidden">
            <div class="upload-content">
                <svg class="mx-auto h-10 w-10 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="mt-3">
                    <p class="text-sm text-gray-600">
                        <button type="button" class="font-medium text-purple-600 hover:text-purple-500">Click to upload</button>
                        or drag and drop
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                        <span class="font-medium">Supported:</span> PNG, JPG, GIF, WEBP •
                        <span class="font-medium text-orange-600" id="php-limits">Max size: Checking...</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Progress Bar -->
        <div id="upload-progress" class="hidden">
            <div class="bg-gray-200 rounded-full h-2">
                <div id="progress-bar" class="bg-gradient-to-r from-purple-600 to-pink-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <p id="progress-text" class="text-sm text-gray-600 mt-2">Uploading...</p>
        </div>

        <!-- Gallery Grid -->
        <div class="space-y-3">
            <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold text-gray-900 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    Your Gallery
                    <span class="ml-2 px-2 py-0.5 bg-purple-100 text-purple-700 text-xs font-medium rounded-full" id="image-count">
                        {{ auth()->user()->galleryImages->count() }} {{ auth()->user()->galleryImages->count() === 1 ? 'photo' : 'photos' }}
                    </span>
                </h3>
                <p class="text-xs text-gray-500">Drag to reorder</p>
            </div>

            <div id="gallery-grid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                @foreach(auth()->user()->galleryImages as $image)
                    <div class="gallery-item relative group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-move" data-id="{{ $image->id }}" data-sort="{{ $image->sort_order }}">
                        <div class="aspect-square">
                            <img src="{{ $image->image_url }}" alt="Gallery Image" loading="lazy" class="w-full h-full object-cover cursor-pointer" onclick="openLightbox('{{ $image->image_url }}')">
                        </div>
                        <button class="delete-btn absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg z-10" onclick="deleteImage({{ $image->id }})">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <button class="view-btn absolute top-2 left-2 w-6 h-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg z-10" onclick="openLightbox('{{ $image->image_url }}')">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"></div>
                    </div>
                @endforeach
            </div>

            @if(auth()->user()->galleryImages->count() === 0)
                <div class="text-center py-6 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50">
                    <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No images yet</h3>
                    <p class="mt-1 text-xs text-gray-500">Upload your first image to get started.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="relative max-w-4xl max-h-full p-4">
        <img id="lightbox-image" src="" alt="Gallery Image" class="max-w-full max-h-full object-contain rounded-lg">
        <button onclick="closeLightbox()" class="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full flex items-center justify-center transition-all duration-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
</div>

<!-- Include SortableJS for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<style>
/* Gallery Specific Styles */
.upload-area.dragover {
    border-color: #9333ea !important;
    background-color: #faf5ff !important;
    transform: scale(1.02);
}

.gallery-item {
    transition: all 0.2s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
}

.gallery-item.sortable-ghost {
    opacity: 0.5;
    transform: rotate(5deg);
}

.gallery-item.sortable-chosen {
    transform: scale(1.05);
    z-index: 10;
}

.gallery-item.sortable-drag {
    transform: rotate(5deg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Lightbox styles */
#lightbox {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

#lightbox-image {
    animation: zoomIn 0.3s ease;
}

@keyframes zoomIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    #gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .upload-area {
        padding: 1rem;
    }

    .upload-area svg {
        width: 2rem;
        height: 2rem;
    }

    #lightbox .relative {
        padding: 1rem;
    }

    #lightbox-image {
        max-height: 80vh;
    }
}

/* Enhanced hover effects */
.gallery-item:hover .view-btn,
.gallery-item:hover .delete-btn {
    transform: scale(1.1);
}

.upload-area:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.15);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('gallery-upload');
    const galleryGrid = document.getElementById('gallery-grid');
    const progressContainer = document.getElementById('upload-progress');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const phpLimitsElement = document.getElementById('php-limits');

    // Check PHP limits on page load
    checkPhpLimits();

    // Make gallery sortable
    if (galleryGrid) {
        new Sortable(galleryGrid, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            onEnd: function(evt) {
                updateImageOrder();
            }
        });
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleFiles(files);
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });

    function checkPhpLimits() {
        // Get PHP limits via API
        fetch('/api/gallery/test', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.php_limits) {
                phpLimitsElement.textContent = `${data.php_limits.upload_max_filesize} per file`;
                phpLimitsElement.className = 'font-medium text-orange-600';
            }
        })
        .catch(error => {
            phpLimitsElement.textContent = 'Unknown limit';
            phpLimitsElement.className = 'font-medium text-red-600';
        });
    }

    function handleFiles(files) {
        if (files.length === 0) return;

        const formData = new FormData();
        for (let file of files) {
            formData.append('images[]', file);
        }

        // Show progress
        progressContainer.classList.remove('hidden');
        progressBar.style.width = '0%';
        progressText.textContent = 'Uploading...';

        // Upload files
        fetch('{{ route("gallery.upload") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            progressBar.style.width = '100%';
            progressText.textContent = 'Upload complete!';

            if (data.success) {
                // Add new images to gallery
                data.images.forEach(image => {
                    addImageToGallery(image);
                });

                // Hide empty state if it exists
                const emptyState = galleryGrid.querySelector('.text-center');
                if (emptyState) {
                    emptyState.remove();
                }

                // Update image count
                updateImageCount();

                setTimeout(() => {
                    progressContainer.classList.add('hidden');
                }, 2000);
            } else {
                console.error('Upload failed:', data);
                alert('Upload failed: ' + (data.message || 'Unknown error'));
                progressContainer.classList.add('hidden');
            }
        })
        .catch(error => {
            alert('Upload failed: ' + error.message + '. Please try again.');
            progressContainer.classList.add('hidden');
        });

        // Reset file input
        fileInput.value = '';
    }

    function addImageToGallery(image) {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item relative group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-move';
        galleryItem.setAttribute('data-id', image.id);
        galleryItem.setAttribute('data-sort', image.sort_order);

        galleryItem.innerHTML = `
            <div class="aspect-square">
                <img src="${image.url}" alt="Gallery Image" loading="lazy" class="w-full h-full object-cover cursor-pointer" onclick="openLightbox('${image.url}')">
            </div>
            <button class="delete-btn absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg z-10" onclick="deleteImage(${image.id})">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <button class="view-btn absolute top-2 left-2 w-6 h-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg z-10" onclick="openLightbox('${image.url}')">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </button>
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"></div>
        `;

        galleryGrid.appendChild(galleryItem);
    }

    function updateImageOrder() {
        const items = galleryGrid.querySelectorAll('.gallery-item');
        const imageData = [];

        items.forEach((item, index) => {
            imageData.push({
                id: parseInt(item.getAttribute('data-id')),
                sort_order: index + 1
            });
        });

        fetch('{{ route("gallery.reorder") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ images: imageData })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Reorder failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Reorder error:', error);
        });
    }

    // Make deleteImage function global
    window.deleteImage = function(imageId) {
        if (!confirm('Are you sure you want to delete this image?')) {
            return;
        }

        fetch(`/gallery/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove image from gallery
                const imageElement = document.querySelector(`[data-id="${imageId}"]`);
                if (imageElement) {
                    imageElement.remove();
                }

                // Update image count
                updateImageCount();

                // Show empty state if no images left
                if (galleryGrid.children.length === 0) {
                    galleryGrid.innerHTML = `
                        <div class="text-center py-6 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50 col-span-full">
                            <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No images yet</h3>
                            <p class="mt-1 text-xs text-gray-500">Upload your first image to get started.</p>
                        </div>
                    `;
                }
            } else {
                alert('Delete failed: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Delete failed. Please try again.');
        });
    };

    // Function to update image count
    function updateImageCount() {
        const imageCount = galleryGrid.querySelectorAll('.gallery-item').length;
        const countElement = document.getElementById('image-count');
        if (countElement) {
            countElement.textContent = `${imageCount} ${imageCount === 1 ? 'photo' : 'photos'}`;
        }
    }

    // Lightbox functions
    window.openLightbox = function(imageUrl) {
        const lightbox = document.getElementById('lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        lightboxImage.src = imageUrl;
        lightbox.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    };

    window.closeLightbox = function() {
        const lightbox = document.getElementById('lightbox');
        lightbox.classList.add('hidden');
        document.body.style.overflow = 'auto';
    };

    // Close lightbox on background click
    document.getElementById('lightbox').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    // Close lightbox on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });
});
</script>
